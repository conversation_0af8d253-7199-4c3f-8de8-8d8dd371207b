import request from "@/utils/httpRequest";

export default {
	save: function (inputForm) {
		return request({
			url: "/gzzyz/gzzyz/save",
			method: "post",
			data: inputForm,
		});
	},

	delete: function (ids) {
		return request({
			url: "/gzzyz/gzzyz/delete",
			method: "delete",
			params: { ids: ids },
		});
	},

	queryById: function (id) {
		return request({
			url: "/gzzyz/gzzyz/queryById",
			method: "get",
			params: { id: id },
		});
	},

	list: function (params) {
		return request({
			url: "/gzzyz/gzzyz/list",
			method: "get",
			params: params,
		});
	},

	exportTemplate: function () {
		return request({
			url: "/gzzyz/gzzyz/import/template",
			method: "get",
			responseType: "blob",
		});
	},

	exportExcel: function (params) {
		return request({
			url: "/gzzyz/gzzyz/export",
			method: "get",
			params: params,
			responseType: "blob",
		});
	},

	importExcel: function (data) {
		return request({
			url: "/gzzyz/gzzyz/import",
			method: "post",
			data: data,
		});
	},
};
