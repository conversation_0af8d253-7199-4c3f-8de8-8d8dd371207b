{"enviroment": {"type": "image", "value": "enviroment/20230210103220892_283035.png"}, "background": {"type": "image", "value": "background/icon_20221202000126917_482359.png"}, "ground": {"enable": true, "groundReflect": true, "reflectFactor": 2, "visibleOnEarth": true, "item": [{"code": "g1630396059883", "name": "特效地面1630396059883", "url": "ground/icon_20221202000123369_597191.png", "color": "rgba(73,102,255,1)", "opacity": 1, "maskUrl": "ground/光1.png", "flowColor": "rgba(143,255,242,1)", "glowFactor": 14.6, "animationSpeed": 0.3, "groundClearance": -10, "repeatFactorX": 7, "repeatFactorY": 7}, {"code": "g1655708349659", "name": "特效地面1655708349659", "url": "ground/icon_20221202000124922_842611.png", "color": "rgba(88,0,199,1)", "opacity": 0.56, "repeatFactor": 30, "repeatFactorX": 15, "repeatFactorY": 15, "maskUrl": "ground/光1.png", "flowColor": "rgba(129,129,255,1)", "glowFactor": 17.3, "animationSpeed": 0.5, "groundClearance": -1}, {"code": "g1655708651732", "name": "特效地面1655708651732", "url": "ground/icon_20221202000125062_426485.png", "color": "rgba(69,124,193,1)", "opacity": 0, "repeatFactor": 30, "repeatFactorX": 2, "repeatFactorY": 2, "maskUrl": "ground/光1.png", "flowColor": "rgba(255,190,255,1)", "glowFactor": 15.5, "animationSpeed": 0.5, "groundClearance": -1}]}, "particle": {"item": []}, "class": {"物体": {"enable": true, "fresnel": {"power": 0, "inverse": true}, "color": {"enable": false, "value": "#888888"}, "colorImage": {"enable": false, "color": "#888888"}, "useColormap": true, "colormapIntensity": 1, "colormap": {"0": "rgba(45,168,207,1)", "1": "rgba(135,69,229,1)", "0.06": "rgba(58,52,197,1)", "0.14": "rgba(0,143,255,1)", "0.25": "rgba(135,69,229,1)", "0.46": "rgba(50,34,137,1)", "0.67": "rgba(50,34,137,1)"}, "opacity": 0.95, "wireframe": {"enable": true, "color": "rgba(79,93,255,1)", "glow": true, "opacity": 0.81}, "glow": 1, "useScrollTex": false, "scrollTex": "class/scroll.jpg", "scrollColor": "#ffffff", "scrollSpeed": 0.003, "reflection": {"enable": true, "specularFactor": 1, "metalness": 0.4, "roughness": 0.4}}, "小品": {"enable": true, "fresnel": {"power": 0, "inverse": false}, "color": {"enable": false, "value": "#888888"}, "colorImage": {"enable": false, "color": "#888888"}, "useColormap": true, "colormapIntensity": 1, "colormap": {"0": "rgba(0,69,102,1)", "1": "rgba(74,143,189,1)", "0.06": "rgba(7,103,191,1)", "0.14": "rgba(15,43,114,1)", "0.25": "rgba(0,173,255,1)", "0.46": "rgba(24,7,114,1)", "0.81": "rgba(69,63,227,1)"}, "opacity": 0.69, "wireframe": {"enable": true, "color": "rgba(71,123,255,1)", "glow": true, "opacity": 0.99}, "glow": 1, "useScrollTex": false, "scrollTex": "class/scroll.jpg", "scrollColor": "#ffffff", "scrollSpeed": 0.003, "reflection": {"enable": true, "specularFactor": 1, "metalness": 0.76, "roughness": 0.41000000000000003}}, "主建筑": {"enable": true, "fresnel": {"power": 2, "inverse": true}, "color": {"enable": true, "value": "rgba(111,83,125,1)"}, "colorImage": {"enable": true, "color": "rgba(111,83,125,1)"}, "useColormap": true, "colormapIntensity": 1, "colormap": {"0": "rgba(40,28,56,1)", "1": "rgba(43,10,92,1)", "0.44": "rgba(27,17,139,1)"}, "useScrollTex": true, "scrollTex": "class/reflect1.jpg", "scrollColor": "rgba(30,66,112,1)", "scrollSpeed": -0.001, "opacity": 1, "glow": 2, "wireframe": {"enable": true, "color": "rgba(105,143,243,1)", "glow": true, "opacity": 1}, "reflection": {"enable": true, "specularFactor": 0.7, "metalness": 0.71, "roughness": 0}}, "地面": {"enable": true, "fresnel": {"power": 0.89, "inverse": true}, "color": {"enable": false, "value": "rgba(159,152,255,1)"}, "colorImage": {"enable": false, "color": "rgba(159,152,255,1)"}, "useColormap": true, "colormapIntensity": 1, "colormap": {"0": "rgba(18,25,82,1)", "0.8": "rgba(21,7,62,1)"}, "useScrollTex": false, "scrollTex": "class/scroll.jpg", "scrollColor": "#ffffff", "scrollSpeed": 0.003, "opacity": 0.73, "glow": 1, "wireframe": {"enable": true, "color": "rgba(115,249,255,1)", "glow": true, "opacity": 0.24}, "reflection": {"enable": true, "specularFactor": 0, "metalness": 0.9, "roughness": 0.4}}, "植被": {"enable": true, "fresnel": {"power": 0.55, "inverse": true}, "color": {"enable": true, "value": "rgba(64,64,64,1)"}, "colorImage": {"enable": true, "color": "rgba(64,64,64,1)"}, "useColormap": true, "colormapIntensity": 1, "colormap": {"1": "rgba(35,41,50,1)", "0.44": "rgba(24,26,66,1)"}, "useScrollTex": false, "scrollTex": "class/scroll.jpg", "scrollColor": "#ffffff", "scrollSpeed": 0.003, "opacity": 0.35, "glow": 1, "wireframe": {"enable": false, "color": "#26e2d7", "glow": false, "opacity": 0.99}, "reflection": {"enable": true, "specularFactor": 1, "metalness": 0, "roughness": 0}}, "地板": {"enable": true, "fresnel": {"power": 0, "inverse": false}, "color": {"enable": false, "value": "#888888"}, "colorImage": {"enable": false, "color": "#888888"}, "useColormap": true, "colormapIntensity": 1, "colormap": {"0.53": "rgba(79,121,255,1)", "0.61": "rgba(243,153,255,1)", "0.95": "rgba(21,49,119,1)"}, "opacity": 0.76, "wireframe": {"enable": false, "color": "rgba(52,190,255,1)", "glow": true, "opacity": 1}, "glow": 0, "useScrollTex": false, "scrollTex": "class/icon_20221202000125552_307100.png", "scrollColor": "rgba(134,216,255,1)", "scrollSpeed": 0.003, "reflection": {"enable": true, "specularFactor": 1, "metalness": 0.6, "roughness": 0.2}}, "天花板": {"enable": true, "fresnel": {"power": 0, "inverse": false}, "color": {"enable": false, "value": "#888888"}, "colorImage": {"enable": false, "color": "#888888"}, "useColormap": true, "colormapIntensity": 1, "colormap": {"1": "#1048b5", "0.13": "#070f38"}, "useScrollTex": false, "scrollTex": "class/scroll.jpg", "scrollColor": "#ffffff", "scrollSpeed": 0.003, "opacity": 0.91, "glow": 1, "wireframe": {"enable": false, "color": "#ffffff", "glow": false, "opacity": 0.99}, "reflection": {"enable": false, "specularFactor": 1, "metalness": 0.6, "roughness": 0.2}}, "建筑层顶": {"enable": true, "fresnel": {"power": 0, "inverse": false}, "color": {"enable": false, "value": "#888888"}, "colorImage": {"enable": false, "color": "#888888"}, "useColormap": true, "colormapIntensity": 1, "colormap": {"1": "#1048b5", "0.13": "#070f38"}, "useScrollTex": false, "scrollTex": "class/scroll.jpg", "scrollColor": "#ffffff", "scrollSpeed": 0.003, "opacity": 0.91, "glow": 1, "wireframe": {"enable": false, "color": "#ffffff", "glow": false, "opacity": 0.99}, "reflection": {"enable": false, "specularFactor": 1, "metalness": 0.6, "roughness": 0.2}}, "墙": {"enable": true, "fresnel": {"power": 2, "inverse": true}, "color": {"enable": false, "value": "#888888"}, "colorImage": {"enable": false, "color": "#888888"}, "useColormap": true, "colormapIntensity": 1, "colormap": {"0.22": "rgba(84,69,143,1)", "0.65": "rgba(60,51,122,1)", "0.94": "rgba(58,77,151,1)"}, "opacity": 0.96, "wireframe": {"enable": true, "color": "rgba(255,121,255,1)", "glow": true, "opacity": 0.12}, "glow": 1, "useScrollTex": false, "scrollTex": "class/scroll.jpg", "scrollColor": "#ffffff", "scrollSpeed": 0.003, "reflection": {"enable": true, "specularFactor": 0, "metalness": 0.84, "roughness": 0.18}}, "门窗": {"enable": true, "fresnel": {"power": 0, "inverse": false}, "color": {"enable": false, "value": "#888888"}, "colorImage": {"enable": false, "color": "#888888"}, "useColormap": true, "colormapIntensity": 1, "colormap": {"0": "rgba(84,180,225,1)", "0.44": "rgba(59,13,141,1)", "0.65": "rgba(245,128,226,1)", "0.96": "rgba(66,2,135,1)"}, "opacity": 0.67, "wireframe": {"enable": true, "color": "rgba(126,108,255,1)", "glow": true, "opacity": 0.9400000000000001}, "glow": 1, "useScrollTex": false, "scrollTex": "class/scroll.jpg", "scrollColor": "#ffffff", "scrollSpeed": 0.003, "reflection": {"enable": true, "specularFactor": 1, "metalness": 0.6, "roughness": 0.46}}, "Interlayer": {"enable": true, "fresnel": {"power": 0.1, "inverse": true}, "color": {"enable": true, "value": "rgba(90,149,255,1)"}, "colorImage": {"enable": true, "color": "rgba(90,149,255,1)"}, "useColormap": true, "colormapIntensity": 1, "colormap": {"0.24": "rgba(83,186,255,1)", "0.84": "rgba(0,244,255,1)"}, "opacity": 0.94, "wireframe": {"enable": true, "color": "rgba(255,105,213,1)", "glow": true, "opacity": 1}, "glow": 1, "useScrollTex": false, "scrollTex": "class/defaultAoMap.png", "scrollColor": "rgba(255,0,0,1)", "scrollSpeed": 0.006, "reflection": {"enable": true, "specularFactor": 1, "metalness": 0, "roughness": 0}}, "WindowTop": {"enable": true, "fresnel": {"power": 0, "inverse": false}, "color": {"enable": false, "value": "#888888"}, "colorImage": {"enable": false, "color": "#888888"}, "useColormap": true, "colormapIntensity": 1, "colormap": {"0": "rgba(0,0,173,1)", "1": "rgba(23,95,234,1)"}, "opacity": 0.47000000000000003, "wireframe": {"enable": true, "color": "rgba(229,196,255,1)", "glow": true, "opacity": 1}, "glow": 1, "useScrollTex": false, "scrollTex": "class/scroll.jpg", "scrollColor": "#ffffff", "scrollSpeed": 0.003, "reflection": {"enable": true, "specularFactor": 1, "metalness": 0.76, "roughness": 0.41000000000000003}}, "Window": {"enable": true, "fresnel": {"power": 2, "inverse": true}, "color": {"enable": true, "value": "rgba(189,111,191,1)"}, "colorImage": {"enable": true, "color": "rgba(189,111,191,1)"}, "useColormap": true, "colormapIntensity": 1, "colormap": {"0": "rgba(196,238,255,1)", "1": "rgba(148,152,253,1)", "0.26": "rgba(77,92,129,1)", "0.56": "rgba(128,184,255,1)", "0.8": "rgba(90,202,255,1)"}, "opacity": 0.57, "wireframe": {"enable": true, "color": "rgba(108,196,255,1)", "glow": true, "opacity": 1}, "glow": 1, "useScrollTex": true, "scrollTex": "class/defaultAoMap.png", "scrollColor": "rgba(245,0,255,1)", "scrollSpeed": 0, "reflection": {"enable": true, "specularFactor": 0.29, "metalness": 1, "roughness": 0.2}}, "LOGO": {"enable": true, "fresnel": {"power": 0, "inverse": false}, "color": {"enable": true, "value": "rgba(158,255,247,1)"}, "colorImage": {"enable": true, "color": "rgba(158,255,247,1)"}, "useColormap": false, "colormapIntensity": 1, "colormap": {"0": "rgba(255,112,31,1)", "1": "rgba(247,89,0,1)"}, "opacity": 1, "wireframe": {"enable": true, "color": "rgba(208,246,255,1)", "glow": true, "opacity": 1}, "glow": 1, "useScrollTex": false, "scrollTex": "class/scroll.jpg", "scrollColor": "rgba(255,183,149,1)", "scrollSpeed": 0, "reflection": {"enable": true, "specularFactor": 1, "metalness": 0.37, "roughness": 0}}, "Line01": {"enable": true, "fresnel": {"power": 0, "inverse": false}, "color": {"enable": false, "value": "#888888"}, "colorImage": {"enable": false, "color": "#888888"}, "useColormap": true, "colormapIntensity": 1, "colormap": {"0": "rgba(0,104,173,1)", "0.14": "rgba(6,12,54,1)", "0.4": "rgba(0,145,173,1)"}, "opacity": 0.47000000000000003, "wireframe": {"enable": true, "color": "rgba(135,124,255,1)", "glow": true, "opacity": 0.32}, "glow": 1, "useScrollTex": false, "scrollTex": "class/scroll.jpg", "scrollColor": "#ffffff", "scrollSpeed": 0.003, "reflection": {"enable": true, "specularFactor": 1, "metalness": 0.76, "roughness": 0.41000000000000003}}, "Line02": {"enable": true, "fresnel": {"power": 0, "inverse": false}, "color": {"enable": false, "value": "#888888"}, "colorImage": {"enable": false, "color": "#888888"}, "useColormap": false, "colormapIntensity": 1, "colormap": {"0.06": "rgba(7,103,191,1)", "0.17": "rgba(12,65,76,1)", "0.36": "rgba(0,173,138,1)"}, "opacity": 0.24, "wireframe": {"enable": true, "color": "rgba(129,235,255,1)", "glow": true, "opacity": 0.36}, "glow": 1, "useScrollTex": false, "scrollTex": "class/scroll.jpg", "scrollColor": "#ffffff", "scrollSpeed": 0.003, "reflection": {"enable": false, "specularFactor": 1, "metalness": 0.76, "roughness": 0.41000000000000003}}, "Box": {"enable": true, "fresnel": {"power": 0.46, "inverse": true}, "color": {"enable": false, "value": "rgba(55,52,74,1)"}, "colorImage": {"enable": false, "color": "rgba(55,52,74,1)"}, "useColormap": true, "colormapIntensity": 1, "colormap": {"0": "rgba(26,21,62,1)", "0.9": "rgba(32,13,52,1)"}, "opacity": 0.71, "wireframe": {"enable": false, "color": "rgba(115,125,239,1)", "glow": true, "opacity": 0.1}, "glow": 1, "useScrollTex": false, "scrollTex": "class/uvMap20.png", "scrollColor": "rgba(63,18,141,1)", "scrollSpeed": 0, "reflection": {"enable": true, "specularFactor": 0.15, "metalness": 0.2, "roughness": 0.7}}, "WindowFrame": {"enable": true, "fresnel": {"power": 0.98, "inverse": false}, "color": {"enable": true, "value": "rgba(83,34,211,1)"}, "colorImage": {"enable": true, "color": "rgba(83,34,211,1)"}, "useColormap": false, "colormapIntensity": 1, "colormap": {"0": "rgba(40,118,155,1)", "0.06": "rgba(6,8,54,1)", "0.14": "rgba(4,84,158,1)", "0.25": "rgba(0,74,206,1)", "0.39": "rgba(56,12,76,1)", "0.5": "rgba(0,159,173,1)"}, "opacity": 0.68, "wireframe": {"enable": true, "color": "rgba(77,79,131,1)", "glow": true, "opacity": 1}, "glow": 1, "useScrollTex": false, "scrollTex": "class/scroll.jpg", "scrollColor": "rgba(245,0,255,1)", "scrollSpeed": 0.006, "reflection": {"enable": false, "specularFactor": 0, "metalness": 0.76, "roughness": 0.41000000000000003}}, "WindowBottom": {"enable": true, "fresnel": {"power": 1.42, "inverse": false}, "color": {"enable": false, "value": "#888888"}, "colorImage": {"enable": false, "color": "#888888"}, "useColormap": true, "colormapIntensity": 0.1, "colormap": {"0": "rgba(115,126,255,1)", "1": "rgba(196,161,241,1)"}, "opacity": 0.99, "wireframe": {"enable": true, "color": "rgba(124,161,255,1)", "glow": true, "opacity": 1}, "glow": 1, "useScrollTex": false, "scrollTex": "class/scroll.jpg", "scrollColor": "#ffffff", "scrollSpeed": 0.003, "reflection": {"enable": true, "specularFactor": 0.45, "metalness": 0.83, "roughness": 0.41000000000000003}}, "Road": {"enable": true, "fresnel": {"power": 0, "inverse": false}, "color": {"enable": false, "value": "#888888"}, "colorImage": {"enable": false, "color": "#888888"}, "useColormap": true, "colormapIntensity": 1, "colormap": {"0": "rgba(84,100,219,1)", "1": "rgba(99,15,205,1)", "0.5": "rgba(101,76,203,1)"}, "opacity": 1, "wireframe": {"enable": true, "color": "rgba(31,33,62,1)", "glow": true, "opacity": 0.88}, "glow": 1, "useScrollTex": true, "scrollTex": "class/uvMap20.png", "scrollColor": "rgba(167,2,2,1)", "scrollSpeed": 0.007, "reflection": {"enable": true, "specularFactor": 1, "metalness": 0, "roughness": 0.47}}, "Facade-Main": {"enable": true, "fresnel": {"power": 0.22, "inverse": true}, "color": {"enable": true, "value": "#888888"}, "colorImage": {"enable": true, "color": "#888888"}, "useColormap": true, "colormapIntensity": 1, "colormap": {"0.17": "rgba(22,22,42,1)", "0.24": "rgba(26,23,68,1)", "0.37": "rgba(55,45,70,1)", "0.68": "rgba(64,35,70,1)"}, "opacity": 0.98, "wireframe": {"enable": false, "color": "rgba(245,230,217,1)", "glow": true, "opacity": 0.3}, "glow": 1, "useScrollTex": false, "scrollTex": "class/scroll.jpg", "scrollColor": "#ffffff", "scrollSpeed": 0.003, "reflection": {"enable": true, "specularFactor": 0, "metalness": 0.1, "roughness": 0.6}}, "次要建筑": {"enable": true, "fresnel": {"power": 0, "inverse": false}, "color": {"enable": false, "value": "#888888"}, "colorImage": {"enable": false, "color": "#888888"}, "useColormap": true, "colormapIntensity": 1, "colormap": {"0.97": "#2e6faa", "0.33": "#160b2d", "0.15": "#2f4087", "0.84": "#213c82", "0.73": "#061126"}, "useScrollTex": false, "scrollTex": "class/scroll.jpg", "scrollColor": "#ffffff", "scrollSpeed": 0.003, "opacity": 0.9400000000000001, "glow": 1, "wireframe": {"enable": false, "color": "#3ecde0", "glow": true, "opacity": 0.99}, "reflection": {"enable": true, "specularFactor": 1, "metalness": 0.32, "roughness": 0.1}}, "FacadeMainWin": {"enable": true, "fresnel": {"power": 0, "inverse": true}, "color": {"enable": false, "value": "#888888"}, "colorImage": {"enable": false, "color": "#888888"}, "useColormap": true, "colormapIntensity": 1, "colormap": {"0": "rgba(45,207,181,1)", "1": "rgba(135,69,229,1)", "0.06": "rgba(58,52,197,1)", "0.14": "rgba(0,214,255,1)", "0.25": "rgba(175,118,255,1)", "0.46": "rgba(50,34,137,1)", "0.67": "rgba(50,34,137,1)"}, "opacity": 0.95, "wireframe": {"enable": true, "color": "rgba(240,161,255,1)", "glow": true, "opacity": 0.81}, "glow": 1, "useScrollTex": false, "scrollTex": "class/scroll.jpg", "scrollColor": "#ffffff", "scrollSpeed": 0.003, "reflection": {"enable": true, "specularFactor": 1, "metalness": 0.4, "roughness": 0.4}}, "FacadeMainGlass": {"enable": true, "fresnel": {"power": 0, "inverse": true}, "color": {"enable": false, "value": "#888888"}, "colorImage": {"enable": false, "color": "#888888"}, "useColormap": true, "colormapIntensity": 1, "colormap": {"0": "rgba(152,230,255,1)", "1": "rgba(124,38,255,1)", "0.06": "rgba(58,52,197,1)", "0.14": "rgba(0,143,255,1)", "0.25": "rgba(167,99,255,1)", "0.46": "rgba(49,114,211,1)", "0.67": "rgba(112,86,255,1)"}, "opacity": 0.95, "wireframe": {"enable": true, "color": "rgba(239,156,255,1)", "glow": true, "opacity": 0.81}, "glow": 1, "useScrollTex": false, "scrollTex": "class/scroll.jpg", "scrollColor": "#ffffff", "scrollSpeed": 0.003, "reflection": {"enable": true, "specularFactor": 1, "metalness": 0.4, "roughness": 0.4}}}, "lighting": {"showHelper": false, "ambientLight": {"ambientFlag": true, "intensity": 0.5, "color": 14927103}, "hemisphereLight": {"hemisphereFlag": false, "intensity": 0, "color": 12910591, "groundColor": 0}, "mainLight": {"mainLightFlag": true, "shadow": true, "shadowQuality": "ultra", "intensity": 1.2, "color": 16767886, "alpha": 52, "beta": 327, "flag": true}, "spotLights": [], "position": [2174963.1327167363, 4092292.0492599667, 4381959.054451178], "distance": 2000, "secondaryLight": {"shadow": false, "shadowQuality": "medium", "intensity": 0, "color": 2316749, "alpha": 50, "beta": 145, "name": "secondaryLight", "title": "第二平行光", "flag": false}, "tertiaryLight": {"shadow": false, "shadowQuality": "medium", "intensity": 0.97, "color": 16761084, "alpha": 154, "beta": 92, "name": "tertiaryLight", "title": "第三平行光", "flag": true}}, "postEffect": {"enable": true, "bloom": {"strength": 1.48, "threshold": 0.2, "radius": 0.4, "enabled": false}, "colorCorrection": {"saturation": 1.4, "brightness": 0.005, "exposure": 0.1, "contrast": 1.15, "gamma": 0.8, "enabled": true}, "FXAA": {"enabled": false}, "vignetting": {"color": "0x0f081e", "offset": 1.5, "enabled": true}, "blurEdge": {"offset": 0.5, "enabled": true}, "film": {"scanlinesIntensity": 0, "scanlinesCount": 2048, "noiseIntensity": 0.35, "grayscale": false, "enabled": false}, "chromaticAberration": {"chromaFactor": 0.006, "enabled": true}}, "resourcePrefix": "sdk:/resources/theme/"}