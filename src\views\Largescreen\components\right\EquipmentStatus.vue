<template>
  <div class="equipment-status">
    <div class="panel-header">
      <div class="title">工单分布</div>
    </div>
    <div class="panel-content">

      <div class="year-info">
        <div class="info-label">年度</div>
        <div class="info-value">{{ currentYear }}年</div>
      </div>

      <div class="pie-chart-section">
        <div class="pie-chart-container">
          <Echarts :options="pieChartOption" class="pie-chart" style="width: 100%; height: 100%;"/>
          <div class="pie-center-data">
            <img class="center-ring" src="@/assets/image/yuanhuan.png" alt="center ring" />
            <div class="center-value">{{ centerValue }}kg</div>
            <div class="center-label">中位数</div>
          </div>
        </div>
        <div class="custom-legend">
          <div class="legend-item" v-for="(item, index) in legendData" :key="index">
            <div class="legend-dot" :style="{backgroundColor: item.color}"></div>
            <div class="legend-content">
              <span class="legend-name">{{item.name}}</span>
            </div>
          </div>
        </div>
      </div>


      <div class="day-info">
        <div class="info-label">当日</div>
        <div class="info-value" @click="timeDropdownVisible = !timeDropdownVisible" style="cursor: pointer;">
          {{ selectedTime || '加载中...' }}
        </div>
        <div class="info-arrow" @click="timeDropdownVisible = !timeDropdownVisible" style="cursor: pointer;">
          {{ timeDropdownVisible ? '▲' : '▼' }}
        </div>
        <div v-show="timeDropdownVisible" class="time-dropdown">
          <div
            v-for="timeOption in timeOptions"
            :key="timeOption"
            class="time-option"
            :class="{ active: timeOption === selectedTime }"
            @click="selectTime(timeOption)"
          >
            {{ timeOption }}
          </div>
        </div>
      </div>
      <div class="bar-chart-section">
        <Echarts :options="barChartOption" style="width: 100%; height: 100%;" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'
import { getOrderDayList, getGdfbDay, getGdfbYear } from '@/api/paichanguanli/paichanjh'

const timeOptions = ref([])
const selectedTime = ref('')
const timeDropdownVisible = ref(false)

const gdfbDayData = ref(null)
const gdfbDayLoading = ref(false)

const gdfbYearData = ref(null)
const gdfbYearLoading = ref(false)
// 定时器
let dataTimer = null

// 图例数据
const legendData = [
  { name: '0-60kg', color: '#F2AB27' },
  { name: '60-200kg', color: '#177DDB' },
  { name: '200-680kg', color: '#FFEE35' },
  { name: '680-2000kg', color: '#01E17E' },
  { name: '2000kg以上', color: '#CB17DB' }
]

// 饼图
const pieChartOption = computed(() => {

  const colorGradients = [
    new echarts.graphic.LinearGradient(0, 1, 1, 0, [
      { offset: 0, color: '#733B09' },
      { offset: 1, color: '#F2AB27' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#177DDB' },
      { offset: 1, color: '#0C3A75' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#FFEE35' },
      { offset: 1, color: '#717111' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#01E17E' },
      { offset: 1, color: '#0C7545' }
    ]),
    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
      { offset: 0, color: '#CB17DB' },
      { offset: 1, color: '#700C75' }
    ])
  ]

  // 计算总数用于百分比计算
  const data = [
    {
      value: (gdfbYearData.value && gdfbYearData.value.num1) ? gdfbYearData.value.num1 : 0,
      name: '0-60kg',
      itemStyle: {
        color: colorGradients[0]
      }
    },
    {
      value: (gdfbYearData.value && gdfbYearData.value.num2) ? gdfbYearData.value.num2 : 0,
      name: '60-200kg',
      itemStyle: {
        color: colorGradients[1]
      }
    },
    {
      value: (gdfbYearData.value && gdfbYearData.value.num3) ? gdfbYearData.value.num3 : 0,
      name: '200-680kg',
      itemStyle: {
        color: colorGradients[2]
      }
    },
    {
      value: (gdfbYearData.value && gdfbYearData.value.num4) ? gdfbYearData.value.num4 : 0,
      name: '680-2000kg',
      itemStyle: {
        color: colorGradients[3]
      }
    },
    {
      value: (gdfbYearData.value && gdfbYearData.value.num5) ? gdfbYearData.value.num5 : 0,
      name: '2000kg以上',
      itemStyle: {
        color: colorGradients[4]
      }
    }
  ]

  const total = data.reduce((sum, item) => sum + item.value, 0)

  return {
    backgroundColor: "transparent",
    tooltip: {
      trigger: 'item',
      formatter: function(params) {
        const percentage = total > 0 ? ((params.value / total) * 100).toFixed(1) : '0.0'
        return `${params.name}<br/>数值: ${params.value}<br/>占比: ${percentage}%`
      },
      backgroundColor: 'rgba(13,5,30,.9)',
      borderColor: 'rgba(255,255,255,.5)',
      borderWidth: 1,
      textStyle: {
        color: 'white',
        fontSize: 12
      },
      position: function (point, params, dom, rect, size) {
        let x = point[0] + 20
        let y = point[1]

        return [x, y]
      },
      extraCssText: 'z-index: 9999; box-shadow: 0 4px 12px rgba(0,0,0,0.5);'
    },
    legend: {
      show: false
    },
    graphic: [],
    series: [{
      name: '重量分布',
      type: 'pie',
      roseType: 'radius',

      radius: ['56%', '92%'],
      center: ['50%', '50%'],
      minShowLabelAngle: 0,

      itemStyle: {
        borderRadius: 0,
        borderWidth: 0
      },
      label: {
        show: true,
        position: 'inside',
        color: '#ffffff',
        fontSize: 14,
        fontWeight: 'bold',
        fontFamily: 'YouSheBiaoTiHei, Microsoft YaHei, sans-serif',
        formatter: function(params) {
          const percentage = total > 0 ? ((params.value / total) * 100).toFixed(1) : '0.0'
          return `${percentage}%`
        },
        textStyle: {
          textShadowColor: 'rgba(0,0,0,0.8)',
          textShadowBlur: 3,
          textShadowOffsetX: 1,
          textShadowOffsetY: 1
        }
      },
      labelLine: {
        show: false
      },
      data: data,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0,0,0,0.5)'
        }
      }
    }]
  }
})

// 柱状图配置
const barChartOption = computed(() => {
  // 定义柱状图颜色数组
  const barTopColor = ["#F2AB27", "#177DDB", "#FFEE35", "#01E17E", "#CB17DB"]
  const barBottomColor = ["#733B09", "#0C3A75", "#717111", "#0C7545", "#700C75"]

  // 构建数据
  const data = [
    (gdfbDayData.value && gdfbDayData.value.num1) ? gdfbDayData.value.num1 : 0,
    (gdfbDayData.value && gdfbDayData.value.num2) ? gdfbDayData.value.num2 : 0,
    (gdfbDayData.value && gdfbDayData.value.num3) ? gdfbDayData.value.num3 : 0,
    (gdfbDayData.value && gdfbDayData.value.num4) ? gdfbDayData.value.num4 : 0,
    (gdfbDayData.value && gdfbDayData.value.num5) ? gdfbDayData.value.num5 : 0
  ]

  return {
    backgroundColor: "transparent",
    grid: {
      left: '10%',
      right: '10%',
      bottom: '20%',
      top: '20%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['0-60kg', '60-200kg', '200-680kg', '680-2000kg', '2000kg以上'],
      axisLabel: {
        color: '#ffffff',
        fontSize: 12,
        fontFamily: 'YouSheBiaoTiHei, Microsoft YaHei, sans-serif',
        rotate: 45,
        interval: 0
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#ffffff',
        fontSize: 12,
        fontFamily: 'YouSheBiaoTiHei, Microsoft YaHei, sans-serif'
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
          type: 'dashed'
        }
      }
    },
    series: [{
      type: 'bar',
      data: data.map((value, index) => ({
        value: value,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: barTopColor[index] },
            { offset: 1, color: barBottomColor[index] }
          ])
        }
      })),
      barWidth: '60%',
      label: {
        show: true,
        position: 'top',
        fontSize: 14,
        color: function(params) {
          // 确保使用对应索引的颜色
          return barTopColor[params.dataIndex];
        },
        fontWeight: 'bold',
        fontFamily: 'YouSheBiaoTiHei, Microsoft YaHei, sans-serif',
        offset: [0, -8],
        formatter: '{c}'
      }
    }]
  }
})}


const fetchTimeOptions = async () => {
  try {
    const response = await getOrderDayList({ current: 1 })
    console.log('时间选项数据:', response)
    if (response && response.records && response.records.length > 0) {

      const timeSet = new Set()
      const timeRecords = []

      response.records.forEach(record => {
        if (record.time) {

          const date = new Date(record.time)
          const formattedTime = `${date.getFullYear()}年${String(date.getMonth() + 1).padStart(2, '0')}月${String(date.getDate()).padStart(2, '0')}日`
          timeSet.add(formattedTime)
          timeRecords.push({
            originalTime: record.time,
            formattedTime: formattedTime
          })
        }
      })


      timeOptions.value = Array.from(timeSet).sort((a, b) => new Date(b.replace(/年|月|日/g, '').replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3')) - new Date(a.replace(/年|月|日/g, '').replace(/(\d{4})(\d{2})(\d{2})/, '$1-$2-$3')))

      if (timeOptions.value.length > 0) {
        // 生成当日时间格式
        const today = new Date()
        const todayFormatted = `${today.getFullYear()}年${String(today.getMonth() + 1).padStart(2, '0')}月${String(today.getDate()).padStart(2, '0')}日`

        selectedTime.value = todayFormatted
        console.log('设置默认选中时间为当日:', selectedTime.value)


        await fetchGdfbDay(selectedTime.value)
      }
    }
  } catch (error) {
    console.error('获取时间选项失败:', error)
  }
}

// 获取工单分布当日数据
const fetchGdfbDay = async (time) => {
  try {
    gdfbDayLoading.value = true

    let timeParam = time

    // 转日期格式
    if (time.includes('年') && time.includes('月') && time.includes('日')) {

      timeParam = time.replace(/年/g, '-').replace(/月/g, '-').replace(/日/g, '')
    }

    console.log('调用工单分布接口，时间参数:', timeParam)
    const response = await getGdfbDay(timeParam)
    gdfbDayData.value = response
    console.log('工单分布当日数据:', response)
  } catch (error) {
    console.error('获取工单分布当日数据失败:', error)

    gdfbDayData.value = {
      num1: 0,
      num2: 0,
      num3: 0,
      num4: 0,
      num5: 0
    }
  } finally {
    gdfbDayLoading.value = false
  }
}


const selectTime = (time) => {
  selectedTime.value = time
  timeDropdownVisible.value = false

  fetchGdfbDay(time)
}


const handleClickOutside = (event) => {

  const dayInfoElement = event.target.closest('.day-info')
  if (!dayInfoElement && timeDropdownVisible.value) {
    timeDropdownVisible.value = false
  }
}

// 当前年份
const currentYear = computed(() => {
  return new Date().getFullYear()
})

// 饼图中心显示的中位数（从年度接口获取）
const centerValue = computed(() => {

  if (!gdfbYearData.value || gdfbYearData.value.zws === null || gdfbYearData.value.zws === undefined) {
    return '0'
  }


  return gdfbYearData.value.zws.toString()
})

// 获取工单分布年度数据
const fetchGdfbYear = async (year) => {
  try {
    gdfbYearLoading.value = true
    console.log('调用工单分布年度接口，年份参数:', year)
    const response = await getGdfbYear(year)
    gdfbYearData.value = response
    console.log('工单分布年度数据:', response)
  } catch (error) {
    console.error('获取工单分布年度数据失败:', error)

    gdfbYearData.value = {
      num1: 0,
      num2: 0,
      num3: 0,
      num4: 0,
      num5: 0,
      zws: 0
    }
  } finally {
    gdfbYearLoading.value = false
  }
}

// 定期刷新数据
const refreshData = async () => {
  try {
    // 刷新当日数据
    if (selectedTime.value) {
      await fetchGdfbDay(selectedTime.value)
    }
    // 刷新年度数据
    await fetchGdfbYear(currentYear.value)
  } catch (error) {
    console.error('定时刷新工单分布数据失败:', error)
  }
}


onMounted(() => {
  fetchTimeOptions()
  fetchGdfbYear(currentYear.value)

  // 设置定时器，每分钟刷新一次数据
  dataTimer = setInterval(() => {
    refreshData()
  }, 60000) // 60000毫秒 = 1分钟

  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  // 清理定时器
  if (dataTimer) {
    clearInterval(dataTimer)
    dataTimer = null
  }
  // 移除事件监听器
  document.removeEventListener('click', handleClickOutside)
})

</script>

<style scoped lang="scss">
.equipment-status {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;

  .panel-header {
    position: relative;
    height: 45px;
    background: url('@/assets/image/Slice 4.png') no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    padding-left: 20px;

    .title {
      font-family: "YouSheBiaoTiHei", "Microsoft YaHei", "PingFang SC", sans-serif;
      font-weight: 400;
      font-size: 25px;
      color: #FFFFFF;
      line-height: 24px;
      text-shadow: 6px 2px 4px #2281CF;
      text-align: left;
      font-style: normal;
      text-transform: none;
      margin-left: 25px;
    }
  }

  .panel-content {
    flex: 1;
    width: 100%;
    background: url('@/assets/image/Slice 13.png') no-repeat;
    background-size: 100% 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    padding: 10px;


    .year-info {
      height: 30px;
      background: url('@/assets/image/Slice 5.png') no-repeat;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      padding: 20px;
      margin-bottom: 10px;

	margin-left: 10px;

      .info-label {
        color: #00e9ff;
        font-size: 14px;
        margin-right: auto;
        margin-left: 15px;
        margin-bottom: 10px;
      }

      .info-value {
        color: #00e9ff;
        font-size: 14px;
        margin-bottom: 10px;
      }
    }

    // 饼图区域
    .pie-chart-section {
      flex: 1;
      min-height: 180px;
      max-height: 200px;
    //   margin-bottom: 10px;
      display: flex;
      align-items: center;
      gap: 20px;
    //   padding: 10px;
      overflow: hidden;
// background: red;
      .pie-chart-container {
        flex:2;
        height: 100%;

        max-height: 200px;
        position: relative;
		// background: red;
		// width: 600px;

        .pie-chart {
          width: 100%;
          height: 100%;
          position: relative;
          z-index: 10;
		//   background: red;
		//   margin-left: 10px;
        }

        .pie-center-data {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          text-align: center;
          pointer-events: none;
          z-index: 10;
          padding: 10px;
          .center-ring {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
width: 100%;
          }

          .center-value {
            color: #ffffff;
            font-size: 14px;
            font-weight: bold;
            line-height: 1;
            margin-bottom: 2px;
            font-family: MiSans, MiSans;
          }

          .center-label {
            color: #ffffff;
            font-size: 12px;
            font-weight: normal;
            line-height: 1;
            margin-top: 5px;
          }
        }
      }

      .custom-legend {
        flex: 0.8;
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 16px;
        padding-left: 15px;

        .legend-item {
          display: flex;
          align-items: center;
          gap: 12px;

          .legend-dot {
            width: 14px;
            height: 14px;
            border-radius: 50%;
            flex-shrink: 0;
          }

          .legend-content {
            .legend-name {
              color: #ffffff;
              font-size: 14px;
              line-height: 1.2;
              white-space: nowrap;
            }
          }
        }
      }
    }


    .day-info {
      height: 35px;
      background: url('@/assets/image/Slice 5.png') no-repeat;
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      padding: 13px;
      margin-bottom: 10px;
      margin: 10px;
      position: relative;

      .info-label {
        color: #00e9ff;
        font-size: 14px;
        margin-right: auto;
        margin-left: 15px;
        margin-bottom: 10px;
      }

      .info-value {
        color: #00e9ff;
        font-size: 14px;
        margin-left: 15px;
        margin-bottom: 10px;
        transition: color 0.3s ease;

        &:hover {
          color: #ffffff;
        }
      }

      .info-arrow {
        color: #00e9ff;
        font-size: 14px;
        margin-left: 5px;
        margin-bottom: 10px;
        transition: color 0.3s ease, transform 0.3s ease;

        &:hover {
          color: #ffffff;
        }
      }
      .time-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: rgba(8, 58, 69, 0.95);
        border: 1px solid #00e9ff;
        border-radius: 4px;
        z-index: 1000;
        max-height: 200px;
        overflow-y: auto;
        box-shadow: 0 4px 12px rgba(0, 233, 255, 0.3);
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: rgba(8, 58, 69, 0.3);
        }

        &::-webkit-scrollbar-thumb {
          background: #00e9ff;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb:hover {
          background: #ffffff;
        }

        .time-option {
          padding: 8px 15px;
          color: #00e9ff;
          font-size: 14px;
          cursor: pointer;
          transition: all 0.3s ease;
          border-bottom: 1px solid rgba(0, 233, 255, 0.1);

          &:hover {
            background: rgba(0, 233, 255, 0.2);
            color: #ffffff;
          }

          &.active {
            background: rgba(0, 233, 255, 0.3);
            color: #ffffff;
            font-weight: bold;
          }

          &:last-child {
            border-bottom: none;
          }
        }
      }
    }


    .bar-chart-section {
      flex: 1;
      min-height: 180px;
      max-height: 200px;
      overflow: hidden;
	//   background: red;
    }
  }
}
</style>



