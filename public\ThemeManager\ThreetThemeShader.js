THREE.ShaderChunk.theme_paras_fragment = `
struct ThemeParams {
    sampler2D colorMapping;
    float colorMappingIntensity;
    float fresnelPower;
    float specularFactor;
    bool fresnelInverse;
    bool useColormap;
    bool enable;
    bool useScrollTex;
};
uniform ThemeParams themeParams;
`

THREE.ShaderChunk.theme_diffuseColor_fragment = `
if(themeParams.enable) {
  if(themeParams.useColormap) {
    float gray = clamp( dot( diffuseColor.rgb, vec3(0.333, 0.333, 0.333) ), 0.0, 1.0 );
    diffuseColor.rgb = mix( diffuseColor.rgb, texture2D( themeParams.colorMapping, vec2( gray, 0.5 ) ).rgb, themeParams.colorMappingIntensity );
  }
}
`
THREE.ShaderChunk.theme_color_fragment = `
if(themeParams.enable) {
    if (themeParams.fresnelInverse) {
        gl_FragColor.a *= pow( abs( dot( normal, vec3(0., 0., 1.) ) ), themeParams.fresnelPower );
    } else {
        gl_FragColor.a *= pow( 1.0 - abs( dot( normal, vec3(0., 0., 1.) ) ), themeParams.fresnelPower );
    }

  #ifndef USE_AOMAP
        float ambientOcclusion = 1.4;
  #endif
   #if defined(STANDARD) || defined(USE_AOMAP)
      gl_FragColor.rgb *= ambientOcclusion;
   #endif
   gl_FragColor.rgb *= (1.0 + themeParams.specularFactor);
}
`;


export const replaceThemeFragmentShader =  function (shader) {
  shader = insertAfter(shader, '#include <common>', '#include <theme_paras_fragment>')
  shader = insertAfter(shader, '#include <color_fragment>', '#include <theme_diffuseColor_fragment>')
  shader = insertAfter(shader, '#include <tonemapping_fragment>', '#include <theme_color_fragment>')
  return shader
}

THREE.ShaderChunk.theme_world_uv_pars_vertex = `
#ifdef USE_EMISSIVEMAP
struct ThemeParams {
    sampler2D colorMapping;
    float colorMappingIntensity;
    float fresnelPower;
    float specularFactor;
    bool fresnelInverse;
    bool useColormap;
    bool enable;
    bool useScrollTex;
};
uniform ThemeParams themeParams;
#endif
`

THREE.ShaderChunk.theme_world_uv_vertex = `
#ifdef USE_EMISSIVEMAP
  if(themeParams.useScrollTex) {
      vec4 worldPositions = modelMatrix * vec4(position, 1.0);
      vEmissiveMapUv.x = worldPositions.z/30.0;
      vEmissiveMapUv.y = worldPositions.y/30.0 + emissiveMapTransform[2].y;
  }
#endif
`
export const replaceThemeVertexShader =  function (shader) {
  shader = insertAfter(shader, '#include <uv_pars_vertex>', '#include <theme_world_uv_pars_vertex>')
  shader = insertAfter(shader, '#include <uv_vertex>', '#include <theme_world_uv_vertex>')
  return shader
}

// ['standard'].forEach(name => {
//   let shader = THREE.ShaderLib[name] && THREE.ShaderLib[name].fragmentShader
//   if (!shader) {
//     return
//   }
//   THREE.ShaderLib[name].fragmentShader = shader
// })

function insertBefore(shader, keyword, chunk) {
  if (shader.includes(keyword)) {
    shader = shader.replace(keyword, `
      ${chunk}
      ${keyword}
    `)
  } else {
    console.warn('insert fail');
  }
  return shader
}

function insertAfter(shader, keyword, chunk) {
  if (shader.includes(keyword)) {
    shader = shader.replace(keyword, `
      ${keyword}
      ${chunk}
    `)
  } else {
    console.warn('insert fail');
  }
  return shader
}

