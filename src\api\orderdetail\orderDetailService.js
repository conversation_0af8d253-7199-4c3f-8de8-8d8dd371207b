import request from "@/utils/httpRequest";

export default {
	save: function (inputForm) {
		return request({
			url: "/orderdetail/orderDetail/save",
			method: "post",
			data: inputForm,
		});
	},

	delete: function (ids) {
		return request({
			url: "/orderdetail/orderDetail/delete",
			method: "delete",
			params: { ids: ids },
		});
	},

	queryById: function (id) {
		return request({
			url: "/orderdetail/orderDetail/queryById",
			method: "get",
			params: { id: id },
		});
	},

	list: function (params) {
		return request({
			url: "/orderdetail/orderDetail/list",
			method: "get",
			params: params,
		});
	},

	exportTemplate: function () {
		return request({
			url: "/orderdetail/orderDetail/import/template",
			method: "get",
			responseType: "blob",
		});
	},

	exportExcel: function (params) {
		return request({
			url: "/orderdetail/orderDetail/export",
			method: "get",
			params: params,
			responseType: "blob",
		});
	},

	importExcel: function (data) {
		return request({
			url: "/orderdetail/orderDetail/import",
			method: "post",
			data: data,
		});
	},
};
