import request from "@/utils/httpRequest";

export default {
	save: function (inputForm) {
		return request({
			url: "/extension/condition/save",
			method: "post",
			data: inputForm,
		});
	},

	delete: function (ids) {
		return request({
			url: "/extension/condition/delete",
			method: "delete",
			params: { ids: ids },
		});
	},

	queryById: function (id) {
		return request({
			url: "/extension/condition/queryById",
			method: "get",
			params: { id: id },
		});
	},

	list: function (params) {
		return request({
			url: "/extension/condition/list",
			method: "get",
			params: params,
		});
	},
};
