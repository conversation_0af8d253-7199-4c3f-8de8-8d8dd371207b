import request from "@/utils/httpRequest";

export default {
	save: function (inputForm) {
		return request({
			url: "/cxph/cxph/save",
			method: "post",
			data: inputForm,
		});
	},

	delete: function (ids) {
		return request({
			url: "/cxph/cxph/delete",
			method: "delete",
			params: { ids: ids },
		});
	},

	queryById: function (id) {
		return request({
			url: "/cxph/cxph/queryById",
			method: "get",
			params: { id: id },
		});
	},

	list: function (params) {
		return request({
			url: "/cxph/cxph/list",
			method: "get",
			params: params,
		});
	},

	exportTemplate: function () {
		return request({
			url: "/cxph/cxph/import/template",
			method: "get",
			responseType: "blob",
		});
	},

	exportExcel: function (params) {
		return request({
			url: "/cxph/cxph/export",
			method: "get",
			params: params,
			responseType: "blob",
		});
	},

	importExcel: function (data) {
		return request({
			url: "/cxph/cxph/import",
			method: "post",
			data: data,
		});
	},
};
