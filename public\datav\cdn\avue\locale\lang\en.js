export default {
  common: {
    condition: 'condition',
    display: 'display',
    hide: 'hide'
  },
  tip: {
    select: 'Please select',
    input: 'Please input'
  },
  check: {
    checkAll: 'checkAll'
  },
  upload: {
    upload: 'upload',
    tip: 'Drag files here，/'
  },
  time: {
    start: 'start',
    end: 'end',
  },
  date: {
    start: 'start',
    end: 'end',
    t: 'today',
    y: 'yesterday',
    n: 'nearly 7',
    a: 'whole'
  },
  form: {
    printBtn: 'print',
    mockBtn: 'mock',
    submitBtn: 'submit',
    emptyBtn: 'empty'
  },
  crud: {
    filter: {
      addBtn: 'add',
      clearBtn: 'clear',
      resetBtn: 'reset',
      cancelBtn: 'cancel',
      submitBtn: 'submit'
    },
    column: {
      name: 'name',
      hide: 'hide',
      fixed: 'fixed',
      filters: 'filters',
      sortable: 'sortable',
      index: 'index',
      width: 'width'
    },
    tipStartTitle: 'Currently selected',
    tipEndTitle: 'items',
    editTitle: 'edit',
    copyTitle: 'copy',
    addTitle: 'add',
    viewTitle: 'view',
    filterTitle: 'filter',
    showTitle: 'showTitle',
    menu: 'menu',
    addBtn: 'add',
    show: 'show',
    hide: 'hide',
    open: 'open',
    shrink: 'shrink',
    printBtn: 'print',
    excelBtn: 'excel',
    updateBtn: 'update',
    cancelBtn: 'cancel',
    searchBtn: 'search',
    emptyBtn: 'empty',
    menuBtn: 'menu',
    saveBtn: 'save',
    viewBtn: 'view',
    editBtn: 'edit',
    copyBtn: 'copy',
    delBtn: 'delete'
  }
}
  ;
