<!DOCTYPE html>
<html lang="zh-cn">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>VT 3D DEMO</title>
    <!-- <link href="https://cdn.bootcdn.net/ajax/libs/element-ui/2.15.14/theme-chalk/index.min.css" rel="stylesheet"> -->
    <link rel="stylesheet" href="./libs/vt/style/vt.css" />
    <script src="./libs/vt/libs/three.min.js"></script>
    <script src="./libs/vt/vt.js"></script>
    <!-- <script src="https://cdn.bootcdn.net/ajax/libs/vue/2.7.5/vue.min.js"></script> -->
    <!-- <script src="https://cdn.bootcdn.net/ajax/libs/element-ui/2.15.14/index.min.js"></script> -->
    <script src="./libs/jquery.min.js"></script>
    <style>
      html,
      body,
      #app {
        height: 100%;
        margin: 0;
        font-size: 14px;
        overflow: hidden;
        position: relative;
      }

      #container {
        /* background-image: radial-gradient(#FFFFFF, #c7d7f3); */
        background-image: radial-gradient(#959595, #08356d);
      }

      .el-dialog {
        background: rgba(28, 28, 28, 0.85);
        backdrop-filter: blur(6px);
      }

      .el-dialog__body {
        padding: 10px;
      }

      .el-dialog__title,
      .el-table,
      .el-table tr {
        background: transparent !important;
        color: #ffffffdd;
      }

      .el-table--border::after,
      .el-table--group::after,
      .el-table::before {
        display: none !important;
      }

      .el-table--border,
      .el-table--group {
        border: none !important;
      }

      .el-table tr td {
        padding: 5px 10px;
      }

      .el-table tr:nth-child(odd) {
        background-color: rgba(46, 130, 226, 0.15) !important;
      }

      .el-table td,
      .el-table th,
      .el-table tr {
        border-color: transparent;
      }

      #container {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }

      #range {
        /* margin: 200px; */
        width: 300px;
        height: 20px;
        appearance: none;
        -webkit-appearance: none;
        /* 覆盖默认外观，适用于 WebKit 浏览器 */
        -moz-appearance: none;
        /* 覆盖默认外观，适用于 Firefox 浏览器 */
        -ms-appearance: none;
        /* 覆盖默认外观，适用于 IE/Edge 浏览器 */

        outline: none;
        border: none;
        /* background-color: aqua; */
        border-radius: 15px;
        position: absolute;
        bottom: 100px;
        left: calc(50% - 150px);
        z-index: 9;
      }

      #range::-webkit-slider-thumb {
        -webkit-appearance: none;
        /* 覆盖默认外观 */
        width: 20px;
        /* 滑块宽度 */
        height: 20px;
        /* 滑块高度 */
        background-color: rgba(0, 123, 255, 1);
        /* 滑块颜色 */
        border-radius: 10px;
        margin-top: -6px;
        cursor: pointer;
        /* 鼠标样式 */
        transition: box-shadow 0.15s ease-in-out;
        /* 动画效果 */
      }

      #range::-webkit-slider-thumb:hover {
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.5);
        /* 滑块hover效果 */
      }

      #range::-webkit-slider-runnable-track {
        width: 100%;
        /* 轨道宽度 */
        height: 8.4px;
        /* 轨道高度 */
        /* background-color: #06c575; */
        /* 轨道颜色 */
        border-radius: 4.2px;
        /* 圆角 */
      }
      .btns {
        position: absolute;
        z-index: 2;
        left: 50px;
        top: 100px;
        background: rgba(0, 0, 0, 0.2);
        width: 180px;
      }
      .btn {
        width: 140px;
        margin: 20px;
        background-color: #fff;
        color: #333;
        height: 30px;
        text-align: center;
        line-height: 30px;
        border-radius: 10px;
        cursor: pointer;
      }
      .text {
        position: absolute;
        top: 100px;
        left: 500px;
        right: 0;
        color: #fff;
        font-size: 28px;
        z-index: 2;
      }

      .reveal {
        animation: reveal 2s forwards;
      }

      @keyframes reveal {
        to {
          color: fff;
        }
      }
    </style>
    <script>
      let sdkPath = "./libs/vt/"
      let isDevMode = false
      if (isDevMode) {
        VT.setConfig({
          sdkPath: sdkPath,
          server: "http://*************:8000",
          access_token: "ak.a2cc10ae47a0ac664edda10a031bc1d0",
        })
      } else {
        VT.setConfig({
          sdkPath: sdkPath,
        })
      }
      VT.resourceManager.setDracoLoader({ path: `${sdkPath}/libs/draco/` })
    </script>
  </head>

  <body>
    <div id="app">
      <div id="container"></div>
      <input type="range" id="range" min="1" max="100" step="0.01" value="0" />
      <div class="btns">
        <div class="btn" onclick="showDis('油相与钉子连接管','1')">
          油相与钉子连接管
        </div>
        <div class="btn" onclick="showDis('切机端口组件','2')">
          切机端口组件
        </div>
        <div class="btn" onclick="showDis('定子','3')">定子</div>
        <div class="btn" onclick="showDis('水相腔体螺栓','4')">
          水相腔体螺栓
        </div>
        <div class="btn" onclick="showDis('泵轴螺栓','5')">泵轴螺栓</div>
        <div class="btn" onclick="showAllDis()">整体动画</div>
      </div>
      <div class="text"></div>
    </div>
    <script>
      function showAllDis() {
        showDis("油相与钉子连接管", "1")
        setTimeout(() => {
          showDis("切机端口组件", "2")
        }, 3000)
        setTimeout(() => {
          showDis("定子", "3")
        }, 6000)
        setTimeout(() => {
          showDis("水相腔体螺栓", "4")
        }, 9000)
        setTimeout(() => {
          showDis("泵轴螺栓", "5")
        }, 12000)
      }
      var viewer = null
      var loading = null
      const appId = "e6ko2p04p9p0rmreql75"
      initViewer()
      let titlePopup
      let meshs = []
      let names = [
        {
          name: "轴承组件",
        },
        {
          name: "固定螺栓a",
        },
        {
          name: "轴套",
        },
        {
          name: "固定螺栓b",
        },
        {
          name: "转接头",
          left: true,
          // bottom: true
        },
        {
          name: "卡箍b",
          left: true,
          // bottom: true
        },
        {
          name: "水相入口",
        },
        {
          name: "底座",
          bottom: true,
        },
        {
          name: "定子",
        },
        {
          name: "转子",
        },
        {
          name: "固定螺栓c",
        },
        {
          name: "固定螺栓d",
        },
        {
          name: "剪切出口和油向出口",
        },
        {
          name: "视镜连接管",
        },
        {
          name: "垫圈",
        },
        {
          name: "卡箍a",
        },
        {
          name: "视镜",
        },
      ]
      function initViewer() {
        // 加载
        loading = new VT.Loading()
        viewer = new VT.Viewer({
          container: "container",
          app: appId,
          renderer: {
            // // 强制渲染
            forceRender: false,
            // // FPS帧率
            // "fps": 60,
            // // 像素比例
            // "devicePixelRatio": 1,
            postEffect: {
              enable: true,
              FXAA: {
                enable: false,
              },
              colorCorrection: {
                enable: true,
                gamma: 1.2,
              },
              outline: {
                enable: false,
                edgeStrength: 2.4,
                edgeGlow: 1.2,
                edgeThickness: 1.2,
                pulsePeriod: 1.0,
                visibleEdgeColor: "#FFDC27",
                hiddenEdgeColor: "#190a05",
              },
            },
          },
          interact: {
            enabled: true, // 允许交互
            mouseButtonLeft: "pan", // 拖拽
            mouseButtonRight: "rotate", // 旋转
            mouseButtonWheel: "dolly", // 缩放
            mouseButtonMiddle: "pan", // 拖拽
            dollySpeed: 0.4,
          },
        })

        // viewer.cameraControl.control.dampingFactor = 0.25;
        // viewer.cameraControl.control.dollyToCursor = true;

        // 场景加载完后执行事件
        viewer.on("scene.loaded", () => {
          setTimeout(() => {
            loading.remove()
            // meshs = viewer.scene.getMeshes().filter(item=>{
            //     return item.name && item.name.indexOf('Cube') < 0
            // })
            // viewer.scene.getMeshes().map(item=>{
            //     if(item.name && item.name.indexOf('Cube') < 0) {
            //         meshs.push(item)
            //     } else {
            //         if(meshs.find(mesh=>{
            //             return mesh.name === item.parent.name
            //         })) {
            //             return
            //         }
            //         meshs.push(item.parent)
            //     }
            // })
          }, 1000)
          viewer.selector.active()
          console.log(meshs)
          $("#range").on("input", (e) => {
            if (titlePopup) {
              titlePopup.remove()
            }
            window.e = e
            let ind = 0
            let pubDes = 0.4
            // meshs.map((item,index)=>{
            //     item.translateZ(index * Number((item.activeDes || 0) - e.target.value) * 0.001)
            //     item.activeDes = e.target.value;
            // })
            let activespeed = 0
            names.map((mesh, index) => {
              changeDes(mesh, activespeed, e.target.value)
              if (!mesh.bottom && !mesh.right && !mesh.left) {
                activespeed++
              }
              return
              // let item = viewer.scene.getMeshByName(mesh.name)
              // if(item) {
              //     item.translateZ(index * Number((item.activeDes || 0) - e.target.value) * 0.001)
              //     item.activeDes = e.target.value;
              // }
              let item = viewer.scene.getObjectByName(mesh.name)
              if (!mesh.left && !mesh.right && !mesh.bottom) {
                item.translateZ(
                  ind * Number((item.activeDes || 0) - e.target.value) * 0.002
                )
                item.activeDes = e.target.value
                ind++
              } else {
                item.translateZ(
                  ind * Number((item.activeDes || 0) - e.target.value) * 0.002
                )
                item.activeDes = e.target.value
                if (mesh.left) {
                  let des =
                    2 *
                    Number(e.target.value - (item.activeLeftDes || 0)) *
                    0.00003
                  console.log(des, "left")
                  item.translateX(des)
                  item.activeLeftDes = e.target.value
                }
                if (mesh.right) {
                  let des =
                    2 *
                    Number((item.activeRightDes || 0) - e.target.value) *
                    0.00003
                  item.translateX(des)
                  console.log(des, "right")
                  item.activeRightDes = e.target.value
                }
                if (mesh.bottom) {
                  let des =
                    2 *
                    Number((item.activeBottomDes || 0) - e.target.value) *
                    0.00003
                  item.translateY(des)
                  console.log(des, "bottom")
                  item.activeBottomDes = e.target.value
                }
              }
            })
          })

          viewer.viewsControl.setViews(viewer.scene.option.meta.views)
          viewer.cameraControl.keypress = true
          viewer.cameraControl.firstPersonControl.moveSpeed = 0.25
          viewer.rm.setPostEffect({
            enable: true,
            outline: {
              enabled: true,
              edgeThickness: 10,
              edgeStrength: 10,
            },
          })
          // viewer.on("click", (e) => {
          //     viewer.rm.setOutlineObject();
          //     if (e.object && (e.object.name.indexOf("设备") >= 0)) {
          //         viewer.rm.setOutlineObject(e.object);
          //         console.log(e.object.name);
          //     }
          // })
        })
        viewer.on("click", (e) => {
          let object = e.object
          window.ob = object
          viewer.rm.setOutlineObject(null)
          let name = object.name
          if (titlePopup) {
            titlePopup.remove()
          }
          if (object.name) {
            console.log(object.name)
            if (e.object && e.object.name.indexOf("Cube") >= 0) {
              // viewer.rm.setOutlineObject(e.object.parent);
              name = object.parent.name
              // console.log(e.object.name);
            } else {
              // viewer.rm.setOutlineObject(object);
            }
            viewer.rm.setOutlineObject(object)
            titlePopup = new VT.Popup({
              className: "title-popup bigfont",
              closeButton: false,
              offset: [150, -50],
              lineWidth: 2,
              lineColor: "#1FCEFA",
              lineEndPosition: [0, 1],
              linePointSize: 5,
              linePointColor: "#ff9900",
              // lineDash: [0,0],
              tip: false,
            })
              .setPosition(e.position)
              .setHTML(name)
              .addTo(viewer)
              .show()
          }
        })

        viewer.on("dblclick", (e) => {
          let object = e.object
        })

        viewer.on("dbrclick", (e) => {})

        let overObject = null
        // viewer.on("mouseover", (e) => {
        //     if (overObject) {
        //         let props = overObject.properties;
        //         if (props.isBuildingLabel) {
        //             overObject.scale.set(1, 1, 1);
        //         }
        //         overObject = null;
        //     }
        //     overObject = e.object;
        //     if (overObject) {
        //         let props = overObject.properties;
        //         if (props.isBuildingLabel || props.isFloor || props.isRoom) {
        //             viewer.setCursor("pointer");
        //             if (props.isBuildingLabel) {
        //                 overObject.scale.set(1.2, 1.2, 1.2);
        //             }
        //             if (props.isFloor || props.isRoom) {
        //                 viewer.rm.setOutlineObject(overObject);
        //             }
        //         }
        //     } else {
        //         viewer.setCursor(null);
        //         viewer.rm.setOutlineObject(null);
        //     }
        // });
      }
      let canCli = true //是否可以点击按钮执行拆解操作
      function showDis(name, spedf) {
        if (!canCli) {
          return
        }
        canCli = false
        $(".text").html("第" + spedf + "步  -  拆解" + name)
        $(".text").show()
        let arr = [
          {
            name: "切机端口组件",
            children: ["固定螺栓d", "剪切出口和油向出口"],
          },
          {
            name: "油相与钉子连接管",
            children: ["视镜连接管", "垫圈", "卡箍a", "视镜"],
          },
          {
            name: "定子",
            children: ["定子", "底座", "转子", "固定螺栓c"],
          },
          {
            name: "水相腔体螺栓",
            children: ["水相入口", "转接头", "卡箍b", "固定螺栓b"],
          },
          {
            name: "泵轴螺栓",
            children: ["轴套", "固定螺栓a"],
          },
        ]
        if (titlePopup) {
          titlePopup.remove()
        }
        let activeSpd = arr.find((item) => {
          return item.name === name
        })
        let child = []
        let activespeed = 0
        names.map((item, index) => {
          if (activeSpd.children.find((ch) => item.name === ch)) {
            child.push({
              model: item,
              speed: activespeed,
            })
          }
          if (!item.bottom && !item.right && !item.left) {
            activespeed++
          }
        })
        let i = 0
        let startSpd = setInterval(() => {
          if (i > 50) {
            clearInterval(startSpd)
            startSpd = null
            canCli = true
            $(".text").hide()
            return
          }
          child.map((item) => {
            changeDes(item.model, item.speed, i)
          })
          i++
        }, 40)
      }

      function changeDes(mesh, speed, value) {
        let item = viewer.scene.getObjectByName(mesh.name)
        if (!mesh.left && !mesh.right && !mesh.bottom) {
          item.translateZ(speed * Number((item.activeDes || 0) - value) * 0.002)
          item.activeDes = value
        } else {
          item.translateZ(speed * Number((item.activeDes || 0) - value) * 0.002)
          item.activeDes = value
          if (mesh.left) {
            let ndes = 2 * Number(value - (item.activeLeftDes || 0)) * 0.0008
            if (mesh.name === "卡箍b") {
              ndes = 2 * Number(value - (item.activeLeftDes || 0)) * 0.0004
            }
            console.log(ndes, "left", speed, value, mesh.name)
            item.translateX(ndes)
            item.activeLeftDes = value
          }
          if (mesh.right) {
            let ndes = 2 * Number((item.activeRightDes || 0) - value) * 0.0008
            item.translateX(ndes)
            console.log(ndes, "right", speed, value, mesh.name)
            item.activeRightDes = value
          }
          if (mesh.bottom) {
            let ndes = 2 * Number((item.activeBottomDes || 0) - value) * 0.0006
            item.translateY(ndes)
            console.log(ndes, "bottom", speed, value, mesh.name)
            item.activeBottomDes = value
          }
        }
      }
    </script>
  </body>
</html>
