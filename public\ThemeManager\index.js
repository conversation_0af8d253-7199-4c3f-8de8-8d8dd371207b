import {isArray, isNotNil, isString} from "../../utils/common.js";
import ThemeMaterialManage from './ThemeMaterialManage.js'
import ThemeGroundManage from './ThemeGroundManage.js'

class ThemeManager {
    constructor(viewer) {
        this._viewer = viewer
        this.themeMaterialManager = new ThemeMaterialManage(viewer)
        this.themeGroundManager = new ThemeGroundManage(viewer)
    }

    set groundEnable(val) {
      if(this.themeGroundManager) {
        this.themeGroundManager.enable = val
      }
      if(this._json) {
        this._json.ground.enable = val;
      }
    }
    get groundEnable() {
      if(this.themeGroundManager) {
        return this.themeGroundManager.enable
      }
      return false
    }

    get particle() {
      return this._json ? this._json.particle.item : [];
    }

    get ground() {
        return this._json ? this._json.ground.item : [];
    }

    get class() {
        return this._json ? this._json.class : {};
    }

    get tags() {
        return this._json ? Object.keys(this._json.class) : []
    }

    getClassByTag(tag){
      if(!this._json.class[tag]) {
          const defaultClass = {
            "enable": false,
            "fresnel": {
              "power": 0,
              "inverse": false
            },
            "color": {
              "enable": false,
              "value": "#888888"
            },
            "colorImage": {
              "enable": false,
              "color": "#888888"
            },
            "useColormap": true,
            "colormapIntensity": 1,
            "colormap": {
              "0": "#00098e",
              "1": "#175fea",
              "0.25": "#006bce"
            },
            "opacity": 0.47,
            "wireframe": {
              "enable": false,
              "color": "#217cd1",
              "glow": true,
              "opacity": 0.99
            },
            "glow": 1,
            "useScrollTex": false,
            "scrollTex": "uvMap/systemIcons/scroll.jpg",
            "scrollColor": "#ffffff",
            "scrollSpeed": 0.003,
            "reflection": {
              "enable": true,
              "specularFactor": 1,
              "metalness": 0.76,
              "roughness": 0.41
            }
          }
          this._json.class[tag] = defaultClass
      }
      return this._json.class[tag]
    }

    async setTheme(url) {
        this.reset()
        this._oldOption = {
            background: this._viewer.scene.option.background,
            envmap: this._viewer.scene.option.envmap,
        }

        if (!url) {
            return
        }
        let json
        if (isString(url)) {
            this._url = url;
            const response = await fetch(url, {responseType: "json"});
            json = await response.json()
        } else {
            json = url;
        }
        this._json = json;
        this.resourcePrefix = VT.resourceManager.transformUrl(json.resourcePrefix)
        this.themeMaterialManager.resourcePrefix = this.resourcePrefix
        this.themeGroundManager.resourcePrefix = this.resourcePrefix

        this._updateBackground(json.background);
        this._updateEnv(json.enviroment)
        this._updateParticle(json.particle)
        // this._updateLight(json.lighting);
        // this._updateEffect(json.postEffect);
        //
        this.themeMaterialManager.update(json.class)
        this.themeGroundManager.update(json.ground.item)
        this.groundEnable = json.ground.enable
        if(isNotNil(json.externalRadius)){
          this._externalRadius = json.externalRadius
        }
        this.updateSize()
    }
    _externalRadius = 100

    get externalRadius() {
      return this._externalRadius
    }

    set externalRadius(v) {
      this._json.externalRadius = v
      this._externalRadius = v
      this.updateSize()
    }

    updateSize() {
      const result = (new THREE.Box3()).expandByObjectWithFilter(this._viewer.scene)
      const size = result.getSize(new THREE.Vector3()).length();
      this.size = this._externalRadius + size

      this._json.ground.item.forEach(item => {
        item.size = this.size
        this.themeGroundManager.updateNode(item)
      })
      if(this._particleGroup) {
        this._particleGroup.children.forEach(item=>{
          item.updateParticle({
            positionRadius: this.size
          })
        })
      }
    }

    updateParticle() {
        if(this._json && this._json.particle) {
          this._updateParticle(this._json.particle)
        }
    }
    _particleGroup = null
    _updateParticle (particle) {
      if(!this._particleGroup) {
        this._particleGroup  = new THREE.Group()
        this._viewer.scene.add(this._particleGroup)
      }
      this._particleGroup.clear()
      particle.item.forEach(item=>{
        this._particleGroup.add(new VT.FlyParticles({
          position: [0, 0.5, 0],
          scale: [1, 1, 1],
          particle: {
            ...item,
            positionRadius: this.size
          }
        }));
      })
    }

    _updateEffect(effectOption) {
      this._viewer.rm.setPostEffect(effectOption)
    }

    _updateLight(light) {
        this._viewer.scene.setDefaultLights(light)
    }

    /**
     * 更新环境贴图
     * @param option
     * @param option.value
     * @param option.type
     */
    _updateEnv({type, value}) {
        if (!isArray(value)) {
            value = this.resourcePrefix + value;
        } else {
            value = value.map(i => this.resourcePrefix + i)
        }
        if (type === 'skybox' && !isArray(value)) {
            value = [value + "/up.jpg", value + "/rt.jpg", value + "/lf.jpg", value + "/fr.jpg", value + "/dn.jpg", value + "/bk.jpg"]
        }
        this._viewer.scene.setEnvironment(value);
    }

    /**
     * 更新background
     * @param option
     * @param option.value
     * @param option.type
     */
    _updateBackground({type, value} = {}) {
        let params
        if (type === 'color') {
            params = {
                color: value
            }
        }
        if (type === 'skybox') {
            params = {
                skyboxPath: this.resourcePrefix + value
            }
        }
        if (type === 'image') {
            params = {
                image: this.resourcePrefix + value
            }
        }
        this._viewer.scene.setBackground(params);
    }

    reset() {
        if (this._oldOption) {
            this._viewer.scene.setBackground(this._oldOption.background);
            this._viewer.scene.setEnvironment(this._oldOption.envmap);
            this._oldOption = null
        }
        this.themeMaterialManager.reset()
        this.themeGroundManager.reset()
        if(this._particleGroup) {
          this._particleGroup.removeFromParent()
          this._particleGroup = null
        }
        this._json = null
    }

    destroy() {
        if(this._particleGroup) {
          this._particleGroup.removeFromParent()
          this._particleGroup = null
        }
        if (this.themeMaterialManager) {
            this.themeMaterialManager.destroy()
            this.themeMaterialManager = null
        }
        if (this.themeGroundManager) {
            this.themeGroundManager.destroy()
            this.themeGroundManager = null
        }
        this._json = null
    }

    toJSON() {
        return this._json
    }
}

export {
    ThemeManager
}


var canvas = document.createElement("canvas");
canvas.width = 256;
canvas.height = 1;
var canvasContext = canvas.getContext("2d")

function createGradientTexture2(colors, texture) {
    var gradient = canvasContext.createLinearGradient(0, 0, 256, 0);

    // 添加颜色停止点
    for (var position in colors) {
        gradient.addColorStop(+position, colors[position]);
    }

    canvasContext.fillStyle = gradient;
    canvasContext.fillRect(0, 0, 256, 1);

    var pixelData = new Uint8Array(canvasContext.getImageData(0, 0, 256, 1).data.buffer);

    // 如果传入了 texture 参数，则更新该 texture；否则创建一个新的 DataTexture
    if (texture !== undefined) {
        texture.image = {
            data: pixelData,
            width: 256,
            height: 1
        };
    } else {
        texture = new THREE.DataTexture(pixelData, 256, 1);
    }

    // 设置纹理的过滤器
    texture.magFilter = texture.minFilter = THREE.LinearFilter;
    texture.needsUpdate = true;

    return texture;
}


function createGradientTexture(gradientStops, existingTexture) {
    var intervalStart, intervalEnd, data = [];
    for (var stop in gradientStops)
        data.push({
            position: +stop,
            color: gradientStops[stop]
        });
    data.sort(compareStops);
    data.forEach((function (stop, index) {
        intervalStart = 0 === index ? 0 : data[index - 1].position;
        intervalEnd = stop.position;
        canvasContext.fillStyle = stop.color;
        canvasContext.fillRect(
            Math.ceil(256 * intervalStart),
            0,
            Math.ceil(256 * (intervalEnd - intervalStart)),
            1
        );
    }));

    var imageData = new Uint8Array(canvasContext.getImageData(0, 0, 256, 1).data.buffer);

    if (existingTexture) {
        existingTexture.image = {
            data: imageData,
            width: 256,
            height: 1
        };
        return existingTexture;
    } else {
        var newTexture = new THREE.DataTexture(imageData, 256, 1);
        newTexture.magFilter = newTexture.minFilter = THREE.LinearFilter;
        newTexture.needsUpdate = true;
        return newTexture;
    }
}


function compareStops(a, b) {
    return a.position - b.position;
}

