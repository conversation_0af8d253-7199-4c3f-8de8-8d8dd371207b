import request from "@/utils/httpRequest";

// 获取排产计划列表
export const getOrderDayList = (params) => {
  return request({
    url: '/orderday/orderDay/list',
    method: 'get',
    params
  })
}
// 排产新增
export const saveOrderDay = (time) => {
  return request({
    url: '/orderday/orderDay/save',
    method: 'post',
    data: { time }
  })
}

// 删除排产计划
export const deleteOrderDay = (id) => {
  return request({
    url: '/orderday/orderDay/delete',
    method: 'delete',
    params: { ids: id }
  })
}

// 导出排产计划
export const exportOrderDay = (params) => {
  return request({
    url: '/orderday/orderDay/export',
    method: 'get',
    params: params, // 传递时间参数
    responseType: 'blob' // 用于文件下载
  })
}

// 根据ID获取排产计划表数据


// 导入排产详情数据
export const importOrderDetail = (formData) => {
  return request({
    url: '/orderdetail/orderDetail/importOrderDetail',
    method: 'post',
    data: formData,
  })
}

// 修改排产详情顺序：直接发送 { id, orders1 }
export const updateOrders = ({ id, orders1 }) => {
  return request({
    url: '/orderdetail/orderDetail/updateOrders',
    method: 'post',
    data: { id, orders1 }
  })
}



// 一键分析/排序：
export const px = (orderDayId) => {
  return request({
    url: '/orderdetail/orderDetail/px',
    method: 'get',
    params: { orderDayId }
  })
}



// 修改进度
export const updateOrderJd = (payload) => {
	return request({
	  url: '/orderdetail/orderDetail/updateOrderJd',
	  method: 'post',
	  data: payload
	})
}



// 获取工单汇总统计数据
export const getOrderSummary = () => {
	return request({
	  url: '/orderday/orderDay/hz',
	  method: 'get'
	})
  }







// 大屏接口


// 产线平衡
export const getCxph = () => {

  return request({
    url: '/open/api/cxph',
    method: 'get'
  })
}



export const gdList = () => {

	return request({
	  url: '/open/api/gdjd',
	  method: 'get'
	})
  }

// 获取生产动态当日数据
export const getScdtDay = (time) => {
	return request({
	  url: '/open/api/scdt/day',
	  method: 'get',
	  params: { time }
	})
}
// 生产动态-年度
export const getScdtYear = (year) => {
	return request({
	  url: '/open/api/scdt/year',
	  method: 'get',
	  params: { year }
	})
}


// 工单分布-当日
export const getGdfbDay = (time) => {
	return request({
	  url: '/open/api/gdfb/day',
	  method: 'get',
	  params: { time }
	})
}


// 工单分布-年度
export const getGdfbYear = (year) => {
	return request({
	  url: '/open/api/gdfb/year',
	  method: 'get',
	  params: { year }
	})
}

// 生产趋势
export const getScqs = () => {
	return request({
	  url: '/open/api/scqs',
	  method: 'get'
	})
}



