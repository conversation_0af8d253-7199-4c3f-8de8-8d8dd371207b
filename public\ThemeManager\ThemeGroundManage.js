import Ground from "./Ground.js";

export default class ThemeGroundManage {
  constructor(viewer, resourcePrefix = "") {
    this._viewer = viewer
    this.resourcePrefix = resourcePrefix
  }

  _groundCache = new WeakMap()


  set enable(val) {
    if(this._groundGroup) {
      this._groundGroup.visible = val;
    }
  }
  get enable() {
    return this._groundGroup && this._groundGroup.visible
  }

  update(groundList) {
    this.reset()
    if (!this._groundGroup) {
      this._groundGroup = new THREE.Group()
      this._groundGroup.name = 'vaps_theme_ground'
      this._viewer.scene.add(this._groundGroup)
    }

    groundList.forEach((item) => {
      const g = new Ground(item, this.resourcePrefix)
      this._groundCache.set(item, g)
      this._groundGroup.add(g)
    })
  }

  updateNode(item) {
    if (!this._groundCache.has(item)) {
      return
    }
    const ground = this._groundCache.get(item)
    ground.updateMaterial()
  }
  reset() {
    if (this._groundGroup) {
      this._groundGroup.clear()
      this._groundGroup.removeFromParent()
      this._groundGroup = null
    }
  }

  destroy() {
    this.reset()
    this._viewer = null;
    this._groundCache = null;
  }
}
