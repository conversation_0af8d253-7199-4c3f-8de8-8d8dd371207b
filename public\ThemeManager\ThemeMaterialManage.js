import {isNotNil} from "../../utils/common.js";
import {BufferGeometryUtils} from "../../utils/BufferGeometryUtils.js";
import {replaceThemeFragmentShader,replaceThemeVertexShader} from "./ThreetThemeShader.js";

export default class ThemeMaterialManage {
  constructor(viewer) {
    this._viewer = viewer
    this._updateMapOffset = this._updateMapOffset.bind(this)
    this._viewer.rm.on('render', this._updateMapOffset)
  }

  _updateMapOffset() {
    this._mapMap.forEach(({map,style})=>{
      map.offset.y -= style.scrollSpeed;
    })
  }

  _styleClass = {}


  _shouldPassNode(node, tag = node.userData._thing_tag) {
    return !node.isMesh || tag === 'helper' || node._thing_tag === 'helper' || node.userData._thing_wireframe || node.isLineSegments2
  }

  _getTag(node) {
    // return node.userData._thing_tag || Object.keys(this._styleClass)[0]
    return node.userData._thing_tag
  }

  materialCacheMap = new Map()

  updateNode(node) {
    this.traverse(node, (n) => {
      this._updateNode(n)
    })
  }

  _updateNodeStyle(node, style) {
    if (!this.materialCacheMap.has(node)) {
      this.materialCacheMap.set(node, node.material)
    }
    const oldMaterial = this.materialCacheMap.get(node)

    if (Array.isArray(node.material)) {
      node.material = node.material.map((m,index) => {
        return this._replaceMaterial(m, style,oldMaterial[index])
      })
    } else {
      node.material = this._replaceMaterial(node.material, style, oldMaterial)
    }
    if (isNotNil(style.glow)) {
      node._thing_origin_bloomed = node.bloomed;
      node.bloomed = !!style.glow;
    }
  }

  _updateNode(node) {
    const tag = node.userData._thing_tag
    if (!tag) {
      this.resetNode(node)
      return;
    }
    const style = this._styleClass[tag]
    node.traverse(n => {
      if (!(style && style.enable)) {
        this.resetNode(n)
        return;
      }
      if (this._shouldPassNode(n)) {
        return;
      }
      this._updateNodeStyle(n, style)
    })
    if (style && style.enable) {
     requestIdleCallback(()=>{
       this._updateWireFrame(node, style.wireframe)
     },{timeout:2000})
    }
  }

  wireframeNodeCache = new Map()

  _updateWireFrame(node, wireframe) {
    if (wireframe.enable) {
      if (!this.wireframeNodeCache.has(node)) {
        let geometrys = []
        node.traverseVisible(n => {
          if (n.geometry && n.geometry.type!=="LineGeometry" && (n.geometry.attributes && !n.geometry.attributes.position || "EdgesGeometry" != n.geometry.type)) {
            const edges = new THREE.EdgesGeometry(n.geometry, 0.5);
            n.updateMatrixWorld()
            edges.applyMatrix4(n.matrixWorld)
            geometrys.push(edges)
          }
        })
        if (geometrys.length) {
          const line = new THREE.LineSegments(BufferGeometryUtils.mergeBufferGeometries(geometrys));
          line.isEdgeWireframe = true;
          line.renderOrder = 1;
          line.scale.set(1.001, 1.001, 1.001);

          line.userData._thing_wireframe = true;
          line.userData._thing_tag = "helper";
          line.userData.isLocked = true;
          line.applyMatrix4(node.matrixWorld.clone().invert())
          this.wireframeNodeCache.set(node, line)
        }
      }
      const line = this.wireframeNodeCache.get(node)

      if (line) {
        const index = node.children.indexOf(line)
        if (index === -1) {
          node.add(line)
        }
        line.bloomed = wireframe.glow;
        line.material.color.setStyle(wireframe.color);
        if (isNotNil(wireframe.opacity)) {
          line.material.transparent = true;
          line.material.opacity = wireframe.opacity;
        }
      }
    } else {
      if (this.wireframeNodeCache.has(node)) {
        const line = this.wireframeNodeCache.get(node)
        const index = node.children.indexOf(line)
        if (index !== -1) {
          node.remove(line)
        }
      }
    }
  }


  _mapMap = new Map()
  _replaceMaterial(material, style,oldMaterial) {
    if(!material.isMeshStandardMaterial) {
      return material
    }
    let newMaterial

    if(material.isThemeMaterial) {
      newMaterial = material
      newMaterial.opacity = style.opacity
    } else {
      newMaterial = material.clone()
    }

    newMaterial.isThemeMaterial = true;

    if(style.useScrollTex) {
        const mapUrl = this.resourcePrefix  + style.scrollTex
        if(!this._mapMap.get(mapUrl)) {
          const map = new THREE.TextureLoader().load(mapUrl)
          map.wrapT = map.wrapS = THREE.RepeatWrapping
          this._mapMap.set(mapUrl, {
            map,
            style
          })
        }
        newMaterial.emissiveMap = this._mapMap.get(mapUrl).map
        newMaterial.emissiveIntensity = 1.0
        newMaterial.emissive = new THREE.Color(style.scrollColor)
      } else {
        newMaterial.emissiveMap = oldMaterial.emissiveMap
        newMaterial.emissiveIntensity = oldMaterial.emissiveIntensity
        newMaterial.emissive =  oldMaterial.emissive
      }

      if (style.reflection.enable) {
          // newMaterial.envMap = this._viewer.scene.environment
          newMaterial.envMapIntensity = 1.5
          newMaterial.metalness = style.reflection.metalness
          newMaterial.roughness = style.reflection.roughness
      } else {
        newMaterial.envMapIntensity = oldMaterial.envMapIntensity
        newMaterial.metalness = oldMaterial.metalness
        newMaterial.roughness = oldMaterial.roughness
      }
      if (style.color.enable) {
          // newMaterial.map =  createSolidColorTexture(style.color.value);
          newMaterial.color.set(style.color.value)
      } else{
        newMaterial.color.set(oldMaterial.color)
      }

      newMaterial.opacity = (oldMaterial.opacity??1) * style.opacity
      newMaterial.transparent = newMaterial.opacity < 1
      if(!newMaterial.themeParams) {
        newMaterial.themeParams = {}
      }
      newMaterial.themeParams.colorMapping= this._getColorMap(style.colormap)
      newMaterial.themeParams.colorMappingIntensity= style.colormapIntensity
      newMaterial.themeParams.fresnelPower=  style.fresnel.power
      newMaterial.themeParams.fresnelInverse= style.fresnel.inverse
      newMaterial.themeParams.useColormap= style.useColormap
      newMaterial.themeParams.specularFactor= style.reflection.specularFactor
      newMaterial.themeParams.enable= style.enable
      newMaterial.themeParams.useScrollTex= style.useScrollTex


      if(newMaterial.onBeforeCompile.toString() === 'onBeforeCompile(){}') {
        newMaterial.onBeforeCompile = ((shader, renderer) => {
          shader.uniforms.themeParams = {
            value: newMaterial.themeParams
          }
          shader.fragmentShader = replaceThemeFragmentShader(shader.fragmentShader);
          shader.vertexShader = replaceThemeVertexShader(shader.vertexShader);
        })
      }
    newMaterial.needsUpdate = true
    return newMaterial
  }

  _colorMapCache = {}
  _getColorMap(colorMap) {
    const key = JSON.stringify(colorMap)
    if(!this._colorMapCache[key]) {
      this._colorMapCache[key] = createGradientTexture2(colorMap)
    }
    return this._colorMapCache[key]
  }

  update(styleClass = this._styleClass) {
    this._styleClass = styleClass
    this.traverse(this._viewer.scene, (node) => {
      this._updateNode(node)
    })
  }

  updateTag(tag) {
    this.traverse(this._viewer.scene, (node) => {
      if (tag === node.userData._thing_tag) {
        this._updateNode(node)
      }
    })
  }

  resetNode(node) {
    if (this.wireframeNodeCache.has(node)) {
      node.remove(this.wireframeNodeCache.get(node))
    }
    if (this.materialCacheMap.has(node)) {
      node.material = this.materialCacheMap.get(node)
    }
    const tag = node.userData._thing_tag
    if (!tag) {
      return;
    }
    node.traverse(n => {
      if (this._shouldPassNode(n)) {
        return
      }
      if (this.wireframeNodeCache.has(n)) {
        n.remove(this.wireframeNodeCache.get(n))
      }
      if (this.materialCacheMap.has(n)) {
        n.material = this.materialCacheMap.get(n)
      }
    })
  }

  reset() {
    this.traverse(this._viewer.scene, (node) => {
      // if (this._shouldPassNode(node)) {
      //     return;
      // }
      if (isNotNil(node._thing_origin_bloomed)) {
        node.bloomed = node._thing_origin_bloomed
        delete node._thing_origin_bloomed
      } else {
        node.bloomed = undefined
      }
      this.resetNode(node)
    })
    // this.materialCacheMap.clear()
    // this.wireframeNodeCache.clear()
  }

  destroy() {
    this._viewer.rm.off('render', this._updateMapOffset)
    this._mapMap.clear()
    this.reset()
    this._viewer = null;
    this._styleClass = null
    if (this.materialCacheMap) {
      this.materialCacheMap.clear()
    }
    this.materialCacheMap = null
    if (this.wireframeNodeCache) {
      this.wireframeNodeCache.clear()
    }
    this.wireframeNodeCache = null
  }

  traverse(node, callback) {
    const tag = node.userData._thing_tag
    callback(node);
    if (tag) {
      return
    }
    const children = node.children;
    for (let i = 0, l = children.length; i < l; i++) {
      this.traverse(children[i], callback);
    }
  }

}

var canvas = document.createElement("canvas");
canvas.width = 256;
canvas.height = 1;
var canvasContext = canvas.getContext("2d")

function createGradientTexture2(colors, texture) {
  var gradient = canvasContext.createLinearGradient(0, 0, 256, 0);

  // 添加颜色停止点
  for (var position in colors) {
    gradient.addColorStop(+position, colors[position]);
  }

  canvasContext.fillStyle = gradient;
  canvasContext.fillRect(0, 0, 256, 1);

  var pixelData = new Uint8Array(canvasContext.getImageData(0, 0, 256, 1).data.buffer);

  // 如果传入了 texture 参数，则更新该 texture；否则创建一个新的 DataTexture
  if (texture !== undefined) {
    texture.image = {
      data: pixelData,
      width: 256,
      height: 1
    };
  } else {
    texture = new THREE.DataTexture(pixelData, 256, 1);
  }

  // 设置纹理的过滤器
  texture.magFilter = texture.minFilter = THREE.LinearFilter;
  texture.needsUpdate = true;

  return texture;
}


function createGradientTexture(gradientStops, existingTexture) {
  var intervalStart, intervalEnd, data = [];
  for (var stop in gradientStops)
    data.push({
      position: +stop,
      color: gradientStops[stop]
    });
  data.sort(compareStops);
  data.forEach((function (stop, index) {
    intervalStart = 0 === index ? 0 : data[index - 1].position;
    intervalEnd = stop.position;
    canvasContext.fillStyle = stop.color;
    canvasContext.fillRect(
      Math.ceil(256 * intervalStart),
      0,
      Math.ceil(256 * (intervalEnd - intervalStart)),
      1
    );
  }));

  var imageData = new Uint8Array(canvasContext.getImageData(0, 0, 256, 1).data.buffer);

  if (existingTexture) {
    existingTexture.image = {
      data: imageData,
      width: 256,
      height: 1
    };
    return existingTexture;
  } else {
    var newTexture = new THREE.DataTexture(imageData, 256, 1);
    newTexture.magFilter = newTexture.minFilter = THREE.LinearFilter;
    newTexture.needsUpdate = true;
    return newTexture;
  }
}

function createSolidColorTexture(input) {
  const rgb = new THREE.Color(input).multiplyScalar(255).toArray();
  const data = new Uint8Array([
    ...rgb, 255, // 第一个像素 RGBA
    ...rgb, 255, // 第二个像素 RGBA
    ...rgb, 255, // 第三个像素 RGBA
    ...rgb, 255  // 第四个像素 RGBA
  ]);

  // 创建纹理
  const texture = new THREE.DataTexture(data, 2, 2, THREE.RGBAFormat);
  texture.magFilter = THREE.NearestFilter;
  texture.minFilter = THREE.NearestFilter;
  texture.needsUpdate = true;

  return texture;
}

function compareStops(a, b) {
  return a.position - b.position;
}

