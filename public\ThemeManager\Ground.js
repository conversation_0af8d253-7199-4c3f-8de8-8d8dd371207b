import {isNotNil} from "../../utils/common.js";
import {Thing} from "../../objects/index.js";

const THREE = window.THREE;

export default class extends Thing {

  constructor(option, resourcePrefix = "") {
    super();
    this.resourcePrefix = resourcePrefix
    this.option = option;
    this.rotation.x = -Math.PI / 2;
    const geometry = new THREE.PlaneGeometry(1, 1)
    this.mesh = new THREE.Mesh(geometry, this._initMaterial())
    this.updateMaterial(option)
    this.add(this.mesh)
    this.userData.skipBoundingBox = true;
    this.userData.isLocked = true;
    this.userData.hideInTree = true;
    this.userData._thing_tag = "helper";
    this.mesh.userData._thing_tag = "helper";
    this.mesh.userData.skipBoundingBox = true;
    this.mesh.userData.isLocked = true;
    this.mesh.userData.hideInTree = true;


    // console.log('ground', this, this.scene);
  }

  _texureMap = new Map()

  updateMaterial({
                   size,
                   url,
                   opacity,
                   repeatFactorX,
                   repeatFactorY,
                   repeatFactor,
                   maskUrl,
                   color,
                   flowColor,
                   glowFactor,
                   animationSpeed,
                   groundClearance,
                   animationType = "flow"
                 } = this.option) {
    this.mesh.renderOrder = -10 + groundClearance;
    if (!this._texureMap.has(url)) {
      const map = new THREE.TextureLoader().load(this.resourcePrefix + url)
      map.wrapS = map.wrapT = THREE.RepeatWrapping;
      this._texureMap.set(url, map)
    }
    const map = this._texureMap.get(url);
    if (!this._texureMap.has(maskUrl)) {
      const map = new THREE.TextureLoader().load(this.resourcePrefix + maskUrl)
      map.wrapS = map.wrapT = THREE.RepeatWrapping;
      this._texureMap.set(maskUrl, map)
    }
    const maskMap = this._texureMap.get(maskUrl);

    this.uniforms.map.value = map
    this.uniforms.maskMap.value = maskMap
    this.uniforms.alpha.value = opacity
    this.uniforms.repeatFactor.value = [repeatFactorX ?? repeatFactor, repeatFactorY ?? repeatFactor]
    this.uniforms.color.value = new THREE.Color().setStyle(color,)
    this.uniforms.glowFactor.value = glowFactor
    this.uniforms.speed.value = animationSpeed
    this.uniforms.flowColor.value = new THREE.Color().setStyle(flowColor,)

    if (this.mesh.material.fragmentShader !== fragmentShaderMap[animationType]) {
      this.mesh.material.fragmentShader = fragmentShaderMap[animationType]
      this.mesh.material.needsUpdate = true
    }
    if (isNotNil(size)) {
      this.scale.copy({
        "x": size,
        "y": size,
        "z": 1
      })
    }
  }

  setupCommonEvent() {
    super.setupCommonEvent();
    this.update = this.update.bind(this)
    this.addEventListener("added", () => {
      if (this.scene) {
        const {rm} = this.scene.viewer;
        rm.addEventListener("render", this.update);
      }
    })
    this.addEventListener("removed", () => {
      if (this.scene) {
        const {rm} = this.scene.viewer;
        rm.removeEventListener("render", this.update);
      }
    })
  }

  update() {
    this.uniforms.time.value += 0.016799999999989268
  }

  uniforms = {
    map: {
      value: null
    },
    time: {
      value: 0
    },
    opacity: {
      value: 1
    },
    alpha: {
      value: null
    },
    repeatFactor: {
      value: null
    },
    maskMap: {
      value: null
    },
    color: {
      value: null
    },
    glowFactor: {
      value: null
    },
    speed: {
      value: null
    },
    flowColor: {
      value: null
    }
  }

  _initMaterial() {
    const vertexShader = `
#include <common>
#include <logdepthbuf_pars_vertex>
varying vec2 vUv;
varying vec2 mapUv;
uniform vec2 repeatFactor;\n
void main() {
   gl_Position = projectionMatrix * modelViewMatrix * vec4(position,1.);\n
   vUv=uv;
   mapUv=uv*repeatFactor;
   #include <logdepthbuf_vertex>
}       `

    const material = new THREE.ShaderMaterial({
      uniforms: this.uniforms,
      vertexShader: vertexShader,
      transparent: true,
      depthWrite: false,
      defines: {}
    })
    material.roughness = 1
    return material
  }
}

const fragmentShaderMap = {
  flow: `
#include <logdepthbuf_pars_fragment>
varying vec2 vUv;
varying vec2 mapUv;
uniform sampler2D map;
uniform sampler2D maskMap;
uniform float time;
uniform float opacity;
uniform float alpha;
uniform vec3 color;
uniform vec3 flowColor;
uniform float glowFactor;
uniform float speed;

float LinearToSRGB(float c) {
    return (c < 0.0031308) ? c * 12.92 : 1.055 * pow(c, 0.41666) - 0.055;
}
float SRGBToLinear(float c) {
    return (c < 0.04045) ? c * 0.0773993808 : pow((c * 0.9478672986 + 0.0521327014), 2.4);
}

void main() {
    float t=mod(time/5.*speed,1.);
    vec2 uv=abs((vUv-vec2(0.5))*2.0);
    float dis = length(uv);

    float r = t-dis;
    vec4 col=texture2D( map, mapUv );
    vec3 finalCol;
    vec4 mask = texture2D(maskMap, vec2(0.5,r));
    finalCol = mix(color,flowColor,clamp(0.,1.,mask.a*glowFactor));



    float ra = ((1.0 - dis)) * ((alpha)+mask.a*glowFactor)*col.a*opacity;
    //float ra = SRGBToLinear((1.0 - dis)) * ((alpha)+mask.a*glowFactor)*col.a*opacity;
    gl_FragColor= vec4(finalCol.rgb, ra);

     if(gl_FragColor.a < 0.001) {
         gl_FragColor.a = 0.0;
    }
    gl_FragColor = linearToOutputTexel(gl_FragColor);

}
`,
  rotate: `
#include <logdepthbuf_pars_fragment>
varying vec2 vUv;
varying vec2 mapUv;
uniform sampler2D map;
uniform sampler2D maskMap;
uniform float time;
uniform float opacity;
uniform float alpha;
uniform vec3 color;
uniform vec3 flowColor;
uniform float glowFactor;
uniform float speed;
vec2 newUV(vec2 coord,float c,float s)
{
    mat2 m=mat2(c,-s,s,c);
    return m*coord;
}
void main() {
    float t=speed*time;
    vec2 pivot=vec2(0.5,0.5);
    vec2 uv=newUV((vUv-pivot),cos(t),sin(t))+pivot;
    vec4 finalCol;
    if(uv.x>0.&&uv.x<1.&&uv.y>0.&&uv.y<1.)
    {
    finalCol=vec4(color,opacity*alpha*texture2D( map, uv ).a);
    }
    gl_FragColor= clamp(finalCol,0.,1.);
    #include <logdepthbuf_fragment>
}
`
}
